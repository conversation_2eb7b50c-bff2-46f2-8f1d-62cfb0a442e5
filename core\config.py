"""
Voice Agent Configuration Management
Centralized configuration with validation and persistence
"""

import json
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class VoiceAgentConfig:
    """Voice Agent Configuration"""
    
    # Audio Configuration
    sample_rate: int = 48000
    channels: int = 1
    input_chunk_size: int = 1024
    output_chunk_size: int = 1024
    input_device_index: Optional[int] = None
    output_device_index: Optional[int] = None
    
    # Voice Activity Detection
    vad_method: str = "custom"  # "custom", "silero", "webrtc"
    vad_sensitivity: float = 0.3
    vad_min_speech_duration: float = 0.15
    vad_max_silence_duration: float = 0.8
    
    # Speech-to-Text Configuration
    stt_model_size: str = "medium"  # "tiny", "small", "medium", "large"
    stt_device: str = "cuda"  # "cpu", "cuda"
    stt_language: str = "en"
    stt_confidence_threshold: float = 0.3
    
    # AI Engine Configuration
    ai_engine: str = "qwen_vl"  # "qwen_vl", "ollama"
    qwen_model: str = "qwen2.5vl:32b"
    enable_vision: bool = True
    enable_gpu_acceleration: bool = True
    
    # Ollama Configuration
    ollama_host: str = "localhost"
    ollama_port: int = 11434
    max_response_tokens: int = 150
    temperature: float = 0.9
    context_memory_size: int = 20
    
    # Text-to-Speech Configuration
    tts_engine: str = "pyttsx3"  # "auto", "pyttsx3", "f5", "chattts", "coqui", "chatterbox"
    voice_rate: int = 200
    voice_volume: float = 0.9
    voice_id: Optional[str] = None
    
    # Performance Configuration
    performance_monitoring: bool = True
    log_level: str = "INFO"
    enable_stats_logging: bool = True
    target_latency_ms: float = 800.0
    
    # Advanced Configuration
    enable_interruption: bool = True
    enable_conversation_memory: bool = True
    enable_audio_effects: bool = False
    enable_noise_suppression: bool = False
    
    def __post_init__(self):
        """Validate configuration after initialization"""
        self.validate()
    
    def validate(self):
        """Validate configuration values"""
        errors = []
        
        # Audio validation
        if self.sample_rate not in [16000, 22050, 44100, 48000]:
            errors.append(f"Invalid sample_rate: {self.sample_rate}")
        
        if self.channels not in [1, 2]:
            errors.append(f"Invalid channels: {self.channels}")
        
        if not (64 <= self.input_chunk_size <= 8192):
            errors.append(f"Invalid input_chunk_size: {self.input_chunk_size}")
        
        # VAD validation
        if not (0.0 <= self.vad_sensitivity <= 1.0):
            errors.append(f"Invalid vad_sensitivity: {self.vad_sensitivity}")
        
        if self.vad_method not in ["custom", "silero", "webrtc"]:
            errors.append(f"Invalid vad_method: {self.vad_method}")
        
        # STT validation
        if self.stt_model_size not in ["tiny", "small", "medium", "large"]:
            errors.append(f"Invalid stt_model_size: {self.stt_model_size}")
        
        if self.stt_device not in ["cpu", "cuda"]:
            errors.append(f"Invalid stt_device: {self.stt_device}")
        
        # AI validation
        if self.ai_engine not in ["qwen_vl", "ollama"]:
            errors.append(f"Invalid ai_engine: {self.ai_engine}")
        
        if not (0.0 <= self.temperature <= 2.0):
            errors.append(f"Invalid temperature: {self.temperature}")
        
        if not (1 <= self.max_response_tokens <= 1000):
            errors.append(f"Invalid max_response_tokens: {self.max_response_tokens}")
        
        # TTS validation
        if self.tts_engine not in ["auto", "pyttsx3", "f5", "chattts", "coqui", "chatterbox"]:
            errors.append(f"Invalid tts_engine: {self.tts_engine}")
        
        if not (50 <= self.voice_rate <= 400):
            errors.append(f"Invalid voice_rate: {self.voice_rate}")
        
        if not (0.0 <= self.voice_volume <= 1.0):
            errors.append(f"Invalid voice_volume: {self.voice_volume}")
        
        # Performance validation
        if not (100.0 <= self.target_latency_ms <= 5000.0):
            errors.append(f"Invalid target_latency_ms: {self.target_latency_ms}")
        
        if self.log_level not in ["DEBUG", "INFO", "WARNING", "ERROR"]:
            errors.append(f"Invalid log_level: {self.log_level}")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VoiceAgentConfig':
        """Create configuration from dictionary"""
        # Filter out unknown keys
        valid_keys = set(cls.__dataclass_fields__.keys())
        filtered_data = {k: v for k, v in data.items() if k in valid_keys}
        
        return cls(**filtered_data)
    
    def save_to_file(self, filepath: str):
        """Save configuration to JSON file"""
        try:
            config_dict = self.to_dict()
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            logger.info(f"CONFIG: Configuration saved to {filepath}")
            
        except Exception as e:
            logger.error(f"ERROR: Failed to save configuration to {filepath}: {e}")
            raise
    
    @classmethod
    def load_from_file(cls, filepath: str) -> 'VoiceAgentConfig':
        """Load configuration from JSON file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            config = cls.from_dict(data)
            logger.info(f"CONFIG: Configuration loaded from {filepath}")
            return config
            
        except FileNotFoundError:
            logger.warning(f"WARNING: Configuration file {filepath} not found")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"ERROR: Invalid JSON in configuration file {filepath}: {e}")
            raise
        except Exception as e:
            logger.error(f"ERROR: Failed to load configuration from {filepath}: {e}")
            raise
    
    def update_from_dict(self, updates: Dict[str, Any]):
        """Update configuration with new values"""
        for key, value in updates.items():
            if hasattr(self, key):
                setattr(self, key, value)
                logger.debug(f"CONFIG: Updated {key} = {value}")
            else:
                logger.warning(f"WARNING: Unknown configuration key: {key}")
        
        # Re-validate after updates
        self.validate()
    
    def get_audio_config(self) -> Dict[str, Any]:
        """Get audio-specific configuration"""
        return {
            'sample_rate': self.sample_rate,
            'channels': self.channels,
            'input_chunk_size': self.input_chunk_size,
            'output_chunk_size': self.output_chunk_size,
            'input_device_index': self.input_device_index,
            'output_device_index': self.output_device_index
        }
    
    def get_ai_config(self) -> Dict[str, Any]:
        """Get AI-specific configuration"""
        return {
            'ai_engine': self.ai_engine,
            'qwen_model': self.qwen_model,
            'enable_vision': self.enable_vision,
            'ollama_host': self.ollama_host,
            'ollama_port': self.ollama_port,
            'max_response_tokens': self.max_response_tokens,
            'temperature': self.temperature,
            'context_memory_size': self.context_memory_size
        }
    
    def get_performance_config(self) -> Dict[str, Any]:
        """Get performance-specific configuration"""
        return {
            'target_latency_ms': self.target_latency_ms,
            'enable_gpu_acceleration': self.enable_gpu_acceleration,
            'performance_monitoring': self.performance_monitoring,
            'enable_stats_logging': self.enable_stats_logging
        }
    
    def optimize_for_latency(self):
        """Optimize configuration for minimum latency"""
        logger.info("CONFIG: Optimizing for minimum latency")
        
        # Reduce chunk sizes for lower latency
        self.input_chunk_size = 512
        self.output_chunk_size = 512
        
        # Use smaller STT model for speed
        if self.stt_model_size in ["large", "medium"]:
            self.stt_model_size = "small"
        
        # Reduce AI response length
        self.max_response_tokens = min(self.max_response_tokens, 100)
        
        # Increase voice rate for faster speech
        self.voice_rate = min(self.voice_rate + 50, 300)
        
        # Reduce context memory for faster processing
        self.context_memory_size = min(self.context_memory_size, 10)
        
        # Enable GPU acceleration
        self.enable_gpu_acceleration = True
        
        self.validate()
    
    def optimize_for_quality(self):
        """Optimize configuration for maximum quality"""
        logger.info("CONFIG: Optimizing for maximum quality")
        
        # Use larger chunks for better quality
        self.input_chunk_size = 2048
        self.output_chunk_size = 2048
        
        # Use larger STT model for accuracy
        self.stt_model_size = "medium"
        
        # Allow longer AI responses
        self.max_response_tokens = 200
        
        # Slower, more natural speech
        self.voice_rate = max(self.voice_rate - 30, 150)
        
        # More conversation context
        self.context_memory_size = 30
        
        # Lower temperature for more consistent responses
        self.temperature = 0.7
        
        self.validate()
    
    def __str__(self) -> str:
        """String representation of configuration"""
        return f"VoiceAgentConfig(ai_engine={self.ai_engine}, stt_device={self.stt_device}, target_latency={self.target_latency_ms}ms)"


# Configuration validation exception
class ValidationError(Exception):
    """Configuration validation error"""
    pass


# Utility functions
def create_default_config() -> VoiceAgentConfig:
    """Create default configuration"""
    return VoiceAgentConfig()


def load_or_create_config(filepath: str) -> VoiceAgentConfig:
    """Load configuration from file or create default if not found"""
    try:
        return VoiceAgentConfig.load_from_file(filepath)
    except FileNotFoundError:
        logger.info(f"CONFIG: Creating default configuration at {filepath}")
        config = create_default_config()
        config.save_to_file(filepath)
        return config
