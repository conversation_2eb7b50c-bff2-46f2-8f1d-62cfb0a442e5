# Core Audio Processing
numpy>=1.24.0
scipy>=1.10.0
librosa>=0.10.0
soundfile>=0.12.0
pyaudio>=0.2.11

# GPU-Accelerated PyTorch (CUDA 12.1 compatible)
torch>=2.1.0+cu121
torchaudio>=2.1.0+cu121
torchvision>=0.16.0+cu121

# High-Quality TTS Models (Hugging Face)
transformers>=4.35.0
accelerate>=0.24.0
datasets>=2.14.0
diffusers>=0.21.0
speechbrain>=0.5.16

# Specific TTS Models
TTS>=0.22.0
bark>=1.0.0
tortoise-tts>=3.0.0

# GPU-Accelerated STT
faster-whisper>=0.10.0
ctranslate2>=3.20.0

# Image/Vision Processing
Pillow>=10.0.0
opencv-python>=4.8.0

# HTTP and API clients
httpx>=0.24.0
aiohttp>=3.8.0
requests>=2.31.0

# Ollama Integration
ollama>=0.1.0

# Configuration and Environment
pyyaml>=6.0
python-dotenv>=1.0.0
pydantic>=2.0.0

# Async and Concurrency
asyncio>=3.4.3
aiofiles>=23.0.0

# Logging and Monitoring
loguru>=0.7.0

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# Windows-specific (for SAPI TTS fallback)
pywin32>=306; sys_platform == "win32"
comtypes>=1.2.0; sys_platform == "win32"

# Audio Effects and Processing
pedalboard>=0.7.0
resampy>=0.4.0

# Utility libraries
tqdm>=4.65.0
rich>=13.0.0
click>=8.1.0

# Memory optimization
psutil>=5.9.0
GPUtil>=1.4.0
