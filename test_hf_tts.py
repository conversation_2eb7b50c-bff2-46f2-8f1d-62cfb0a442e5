"""
Test Hugging Face TTS Processor
Simple test to verify HF TTS functionality
"""

import sys
import os
import logging
import time

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.threading_infrastructure import Lock<PERSON>reeQueue, ProcessingResult
from core.huggingface_tts_processor import HuggingFaceTTSThread

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_hf_tts():
    """Test Hugging Face TTS functionality"""
    print("Testing Hugging Face TTS...")
    
    try:
        # Create test queues
        input_queue = LockFreeQueue(maxsize=100, name="HFTTSInputTest")
        output_queue = LockFreeQueue(maxsize=100, name="HFTTSOutputTest")
        
        # Create HF TTS thread - try VITS model first
        tts = HuggingFaceTTSThread(
            input_queue=input_queue,
            output_queue=output_queue,
            model_name="facebook/mms-tts-eng",  # Multilingual TTS model
            device="cuda",  # Try CUDA for better performance
            voice_preset="female",
            speech_rate=1.0,
            volume=0.9
        )
        
        print("Initializing TTS model (this may take a moment)...")
        
        # Test initialization
        if tts.initialize():
            print("SUCCESS: Hugging Face TTS initialized")
            
            # Test text cleaning
            test_text = "Hello, this is a test of the Hugging Face TTS system!"
            cleaned = tts._clean_text_for_tts(test_text)
            print(f"Text cleaned: '{test_text}' -> '{cleaned}'")
            
            # Create a test processing result
            test_result = ProcessingResult(
                data={'text': test_text},
                timestamp=time.time(),
                processing_time_ms=0,
                metadata={'source': 'test'}
            )
            
            # Test synthesis
            print("Testing speech synthesis...")
            start_time = time.time()
            audio_result = tts._synthesize_speech(test_result)
            synthesis_time = time.time() - start_time
            
            if audio_result:
                audio_data = audio_result.data['audio_data']
                sample_rate = audio_result.data['sample_rate']
                print(f"SUCCESS: Generated {len(audio_data)} audio samples at {sample_rate} Hz")
                print(f"Synthesis time: {synthesis_time:.2f} seconds")
                print(f"Audio duration: {len(audio_data) / sample_rate:.2f} seconds")
                
                # Get TTS stats
                stats = tts.get_tts_stats()
                print("\nTTS Statistics:")
                for key, value in stats.items():
                    print(f"  {key}: {value}")
                
            else:
                print("ERROR: Speech synthesis failed")
            
            # Cleanup
            tts.cleanup()
            print("SUCCESS: Hugging Face TTS test completed")
            
        else:
            print("ERROR: Hugging Face TTS initialization failed")
            
    except Exception as e:
        print(f"ERROR: Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_hf_tts()
