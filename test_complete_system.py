"""
Complete System Test
Test all components of the upgraded voice agent system
"""

import sys
import os
import logging
import time
import json

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.config import VoiceAgentConfig
from core.huggingface_tts_processor import Hugging<PERSON><PERSON>TTSThread, HUGGINGFACE_AVAILABLE
from core.threading_infrastructure import Lock<PERSON>reeQueue, ProcessingResult

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_dependencies():
    """Test all required dependencies"""
    print("=== Testing Dependencies ===")
    
    dependencies = {
        'torch': 'PyTorch',
        'transformers': 'Hugging Face Transformers',
        'datasets': 'Hugging Face Datasets',
        'accelerate': 'Hugging Face Accelerate',
        'soundfile': 'SoundFile',
        'numpy': 'NumPy'
    }
    
    results = {}
    for module, name in dependencies.items():
        try:
            __import__(module)
            results[name] = "✓ Available"
            print(f"  {name}: ✓ Available")
        except ImportError as e:
            results[name] = f"✗ Missing: {e}"
            print(f"  {name}: ✗ Missing: {e}")
    
    return results

def test_configuration():
    """Test configuration system"""
    print("\n=== Testing Configuration ===")
    
    try:
        # Create configuration
        config = VoiceAgentConfig(
            tts_engine="huggingface",
            tts_model_name="facebook/mms-tts-eng",
            tts_device="cpu",
            voice_preset="female",
            speech_rate=1.0,
            voice_volume=0.9
        )
        
        print("  Configuration creation: ✓ Success")
        
        # Validate configuration
        errors = config.validate()
        if not errors:
            print("  Configuration validation: ✓ Success")
            return config
        else:
            print(f"  Configuration validation: ✗ Errors: {errors}")
            return None
            
    except Exception as e:
        print(f"  Configuration test: ✗ Failed: {e}")
        return None

def test_huggingface_tts():
    """Test Hugging Face TTS system"""
    print("\n=== Testing Hugging Face TTS ===")
    
    if not HUGGINGFACE_AVAILABLE:
        print("  Hugging Face TTS: ✗ Not available")
        return False
    
    try:
        # Create test queues
        input_queue = LockFreeQueue(maxsize=100, name="TTSTestInput")
        output_queue = LockFreeQueue(maxsize=100, name="TTSTestOutput")
        
        # Create TTS thread
        tts = HuggingFaceTTSThread(
            input_queue=input_queue,
            output_queue=output_queue,
            model_name="facebook/mms-tts-eng",
            device="cpu",
            voice_preset="female",
            speech_rate=1.0,
            volume=0.9
        )
        
        print("  TTS Thread creation: ✓ Success")
        
        # Test initialization
        if tts.initialize():
            print("  TTS Initialization: ✓ Success")
            
            # Test synthesis
            test_result = ProcessingResult(
                data={'text': "This is a test of the upgraded voice agent system."},
                timestamp=time.time(),
                processing_time_ms=0,
                metadata={'source': 'test'}
            )
            
            start_time = time.time()
            audio_result = tts._synthesize_speech(test_result)
            synthesis_time = time.time() - start_time
            
            if audio_result:
                audio_data = audio_result.data['audio_data']
                sample_rate = audio_result.data['sample_rate']
                duration = len(audio_data) / sample_rate
                
                print(f"  TTS Synthesis: ✓ Success")
                print(f"    - Audio samples: {len(audio_data):,}")
                print(f"    - Sample rate: {sample_rate:,} Hz")
                print(f"    - Duration: {duration:.2f} seconds")
                print(f"    - Synthesis time: {synthesis_time:.2f} seconds")
                print(f"    - Real-time factor: {synthesis_time/duration:.2f}x")
                
                # Get stats
                stats = tts.get_tts_stats()
                print(f"    - Success rate: {stats['success_rate']:.1%}")
                print(f"    - Chars/second: {stats['chars_per_second']:.1f}")
                
                tts.cleanup()
                return True
            else:
                print("  TTS Synthesis: ✗ Failed")
                tts.cleanup()
                return False
        else:
            print("  TTS Initialization: ✗ Failed")
            return False
            
    except Exception as e:
        print(f"  TTS Test: ✗ Exception: {e}")
        return False

def test_performance():
    """Test performance characteristics"""
    print("\n=== Testing Performance ===")
    
    try:
        # Test multiple synthesis operations
        input_queue = LockFreeQueue(maxsize=100, name="PerfTestInput")
        output_queue = LockFreeQueue(maxsize=100, name="PerfTestOutput")
        
        tts = HuggingFaceTTSThread(
            input_queue=input_queue,
            output_queue=output_queue,
            model_name="facebook/mms-tts-eng",
            device="cpu"
        )
        
        if not tts.initialize():
            print("  Performance test: ✗ TTS initialization failed")
            return False
        
        # Test texts of different lengths
        test_texts = [
            "Hello.",
            "This is a medium length test sentence.",
            "This is a much longer test sentence that contains more words and should take longer to synthesize, allowing us to test the performance characteristics of the TTS system."
        ]
        
        results = []
        for i, text in enumerate(test_texts):
            test_result = ProcessingResult(
                data={'text': text},
                timestamp=time.time(),
                processing_time_ms=0,
                metadata={'source': f'perf_test_{i}'}
            )
            
            start_time = time.time()
            audio_result = tts._synthesize_speech(test_result)
            synthesis_time = time.time() - start_time
            
            if audio_result:
                audio_data = audio_result.data['audio_data']
                sample_rate = audio_result.data['sample_rate']
                duration = len(audio_data) / sample_rate
                rtf = synthesis_time / duration
                
                results.append({
                    'text_length': len(text),
                    'synthesis_time': synthesis_time,
                    'audio_duration': duration,
                    'rtf': rtf
                })
                
                print(f"  Test {i+1}: {len(text)} chars -> {synthesis_time:.2f}s (RTF: {rtf:.2f}x)")
        
        if results:
            avg_rtf = sum(r['rtf'] for r in results) / len(results)
            print(f"  Average RTF: {avg_rtf:.2f}x")
            print(f"  Performance: {'✓ Excellent' if avg_rtf < 0.3 else '✓ Good' if avg_rtf < 0.5 else '⚠ Acceptable'}")
        
        tts.cleanup()
        return True
        
    except Exception as e:
        print(f"  Performance test: ✗ Exception: {e}")
        return False

def generate_report(results):
    """Generate a comprehensive test report"""
    print("\n" + "="*50)
    print("VOICE AGENT UPGRADE TEST REPORT")
    print("="*50)
    
    # System info
    import torch
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    print(f"Device count: {torch.cuda.device_count()}")
    
    # Test results summary
    print("\nTest Results:")
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {test_name}: {status}")
    
    # Overall status
    all_passed = all(results.values())
    print(f"\nOverall Status: {'✓ ALL TESTS PASSED' if all_passed else '⚠ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 Voice Agent upgrade completed successfully!")
        print("   - Hugging Face TTS is working")
        print("   - Performance is excellent")
        print("   - System is ready for use")
    else:
        print("\n⚠ Some issues detected. Please review failed tests.")

def main():
    """Run complete system test"""
    print("Voice Agent System Test")
    print("Testing upgraded system with Hugging Face TTS")
    print("="*50)
    
    # Run all tests
    results = {}
    
    # Test dependencies
    dep_results = test_dependencies()
    results['Dependencies'] = all('Available' in status for status in dep_results.values())
    
    # Test configuration
    config = test_configuration()
    results['Configuration'] = config is not None
    
    # Test TTS
    results['Hugging Face TTS'] = test_huggingface_tts()
    
    # Test performance
    results['Performance'] = test_performance()
    
    # Generate report
    generate_report(results)

if __name__ == "__main__":
    main()
