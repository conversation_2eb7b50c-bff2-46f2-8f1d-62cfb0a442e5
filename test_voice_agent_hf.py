"""
Test Voice Agent with Hugging Face TTS
Quick test to verify the voice agent works with the new HF TTS
"""

import sys
import os
import logging
import time
import threading

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ultra_voice_agent import UltraVoiceAgent
from core.config import VoiceAgentConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_voice_agent_hf():
    """Test voice agent with Hugging Face TTS"""
    print("Testing Voice Agent with Hugging Face TTS...")
    
    try:
        # Create configuration with HF TTS
        config = VoiceAgentConfig(
            # TTS Configuration
            tts_engine="huggingface",
            tts_model_name="facebook/mms-tts-eng",
            tts_device="cpu",  # Use CPU since CUDA has issues
            voice_preset="female",
            speech_rate=1.0,
            voice_volume=0.9,

            # AI Configuration
            ai_engine="qwen_vl",
            qwen_model="qwen2.5vl:32b",
            temperature=0.7,
            max_response_tokens=150,

            # Audio Configuration
            sample_rate=16000,
            input_chunk_size=1024,
            output_chunk_size=1024,
            channels=1,

            # Performance Configuration
            performance_monitoring=True
        )
        
        print("Configuration created successfully")
        
        # Validate configuration
        errors = config.validate()
        if errors:
            print(f"Configuration errors: {errors}")
            return
        
        print("Configuration validated successfully")
        
        # Create voice agent
        agent = UltraVoiceAgent(config)
        print("Voice agent created successfully")
        
        # Test initialization (without starting full system)
        print("Testing TTS initialization...")
        
        # Create a simple test by initializing just the TTS component
        from core.threading_infrastructure import LockFreeQueue
        from core.huggingface_tts_processor import HuggingFaceTTSThread
        
        # Create test queues
        input_queue = LockFreeQueue(maxsize=100, name="TestInput")
        output_queue = LockFreeQueue(maxsize=100, name="TestOutput")
        
        # Create TTS thread
        tts_thread = HuggingFaceTTSThread(
            input_queue=input_queue,
            output_queue=output_queue,
            model_name=config.tts_model_name,
            device=config.tts_device,
            voice_preset=config.voice_preset,
            speech_rate=config.speech_rate,
            volume=config.voice_volume
        )
        
        # Test TTS initialization
        if tts_thread.initialize():
            print("SUCCESS: TTS thread initialized successfully")
            
            # Test a simple synthesis
            from core.threading_infrastructure import ProcessingResult
            test_result = ProcessingResult(
                data={'text': "Hello! The voice agent is working with Hugging Face TTS."},
                timestamp=time.time(),
                processing_time_ms=0,
                metadata={'source': 'test'}
            )
            
            print("Testing speech synthesis...")
            start_time = time.time()
            audio_result = tts_thread._synthesize_speech(test_result)
            synthesis_time = time.time() - start_time
            
            if audio_result:
                audio_data = audio_result.data['audio_data']
                sample_rate = audio_result.data['sample_rate']
                duration = len(audio_data) / sample_rate
                
                print(f"SUCCESS: Generated {len(audio_data)} samples at {sample_rate} Hz")
                print(f"Audio duration: {duration:.2f} seconds")
                print(f"Synthesis time: {synthesis_time:.2f} seconds")
                print(f"Real-time factor: {synthesis_time/duration:.2f}x")
                
                # Get TTS stats
                stats = tts_thread.get_tts_stats()
                print(f"TTS Engine: {stats['tts_engine']}")
                print(f"Model: {stats['model_name']}")
                print(f"Device: {stats['device']}")
                print(f"Success rate: {stats['success_rate']:.2%}")
                
            else:
                print("ERROR: Speech synthesis failed")
            
            # Cleanup
            tts_thread.cleanup()
            print("TTS thread cleaned up")
            
        else:
            print("ERROR: TTS thread initialization failed")
        
        print("Voice agent test completed successfully")
        
    except Exception as e:
        print(f"ERROR: Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_voice_agent_hf()
