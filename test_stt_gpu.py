"""
Test STT GPU Acceleration
Test Faster-Whisper with CUDA acceleration
"""

import sys
import os
import logging
import time
import numpy as np

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.stt_processor import <PERSON><PERSON>hread, FASTER_WHISPER_AVAILABLE
from core.threading_infrastructure import Lock<PERSON><PERSON>Q<PERSON><PERSON>, ProcessingResult

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_cuda_availability():
    """Test CUDA availability for Faster-Whisper"""
    print("=== Testing CUDA Availability ===")
    
    try:
        import torch
        print(f"PyTorch CUDA available: {torch.cuda.is_available()}")
        print(f"CUDA device count: {torch.cuda.device_count()}")
        if torch.cuda.is_available():
            print(f"CUDA device name: {torch.cuda.get_device_name(0)}")
            print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    except ImportError:
        print("PyTorch not available")
    
    try:
        import ctranslate2
        print(f"CTranslate2 version: {ctranslate2.__version__}")
        print(f"CTranslate2 CUDA support: {ctranslate2.get_cuda_device_count() > 0}")
        print(f"CTranslate2 CUDA devices: {ctranslate2.get_cuda_device_count()}")
    except ImportError:
        print("CTranslate2 not available")
    
    if not FASTER_WHISPER_AVAILABLE:
        print("ERROR: Faster-Whisper not available")
        return False
    
    try:
        from faster_whisper import WhisperModel
        print("Faster-Whisper available: ✓")
        return True
    except ImportError as e:
        print(f"Faster-Whisper import error: {e}")
        return False

def test_stt_device(device="cuda", model_size="tiny"):
    """Test STT with specific device"""
    print(f"\n=== Testing STT on {device.upper()} ===")
    
    try:
        # Create test queues
        input_queue = LockFreeQueue(maxsize=100, name="STTTestInput")
        output_queue = LockFreeQueue(maxsize=100, name="STTTestOutput")
        
        # Create STT thread
        stt = STTThread(
            input_queue=input_queue,
            output_queue=output_queue,
            model_size=model_size,
            device=device,
            language="en",
            confidence_threshold=0.3
        )
        
        print(f"STT Thread created for {device}")
        
        # Test initialization
        if stt.initialize():
            print(f"STT Initialization on {device}: ✓ Success")
            
            # Create test audio (3 seconds of sine wave at 440Hz)
            sample_rate = 16000
            duration = 3.0
            t = np.linspace(0, duration, int(sample_rate * duration))
            # Create a simple tone that might be recognized as speech
            audio_data = (np.sin(2 * np.pi * 440 * t) * 0.1).astype(np.float32)
            
            # Create test speech result
            test_result = ProcessingResult(
                data={
                    'audio_data': audio_data,
                    'sample_rate': sample_rate,
                    'duration': duration,
                    'confidence': 0.8
                },
                timestamp=time.time(),
                processing_time_ms=0,
                metadata={'source': 'test'}
            )
            
            print(f"Testing transcription on {device}...")
            start_time = time.time()
            
            # Test transcription
            transcription_result = stt._transcribe_speech(test_result)
            
            processing_time = time.time() - start_time
            
            if transcription_result:
                text = transcription_result.data.get('text', '')
                confidence = transcription_result.data.get('confidence', 0)
                
                print(f"Transcription on {device}: ✓ Success")
                print(f"  Text: '{text}'")
                print(f"  Confidence: {confidence:.2f}")
                print(f"  Processing time: {processing_time:.2f}s")
                print(f"  Audio duration: {duration:.2f}s")
                print(f"  Real-time factor: {processing_time/duration:.2f}x")
                
                # Get STT stats
                stats = stt.get_stt_stats()
                print(f"  Model: {stats['model_size']}")
                print(f"  Device: {stats['device']}")
                print(f"  Success rate: {stats['success_rate']:.1%}")
                
            else:
                print(f"Transcription on {device}: ✗ Failed")
            
            # Cleanup
            stt.cleanup()
            return True
            
        else:
            print(f"STT Initialization on {device}: ✗ Failed")
            return False
            
    except Exception as e:
        print(f"STT Test on {device}: ✗ Exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """Compare CPU vs CUDA performance"""
    print("\n=== Performance Comparison ===")
    
    devices = ["cpu"]
    
    # Check if CUDA is available
    try:
        import ctranslate2
        if ctranslate2.get_cuda_device_count() > 0:
            devices.append("cuda")
    except:
        pass
    
    results = {}
    
    for device in devices:
        print(f"\nTesting {device.upper()} performance...")
        
        try:
            # Create test queues
            input_queue = LockFreeQueue(maxsize=100, name=f"PerfTest{device}")
            output_queue = LockFreeQueue(maxsize=100, name=f"PerfOut{device}")
            
            # Create STT thread
            stt = STTThread(
                input_queue=input_queue,
                output_queue=output_queue,
                model_size="tiny",  # Use tiny for faster testing
                device=device,
                language="en"
            )
            
            if stt.initialize():
                # Test with different audio lengths
                test_durations = [1.0, 3.0, 5.0]
                device_results = []
                
                for duration in test_durations:
                    # Create test audio
                    sample_rate = 16000
                    t = np.linspace(0, duration, int(sample_rate * duration))
                    audio_data = (np.sin(2 * np.pi * 440 * t) * 0.1).astype(np.float32)
                    
                    test_result = ProcessingResult(
                        data={
                            'audio_data': audio_data,
                            'sample_rate': sample_rate,
                            'duration': duration
                        },
                        timestamp=time.time(),
                        processing_time_ms=0,
                        metadata={'source': f'perf_test_{duration}s'}
                    )
                    
                    # Time the transcription
                    start_time = time.time()
                    result = stt._transcribe_speech(test_result)
                    processing_time = time.time() - start_time
                    
                    if result:
                        rtf = processing_time / duration
                        device_results.append({
                            'duration': duration,
                            'processing_time': processing_time,
                            'rtf': rtf
                        })
                        print(f"  {duration}s audio -> {processing_time:.2f}s (RTF: {rtf:.2f}x)")
                
                if device_results:
                    avg_rtf = sum(r['rtf'] for r in device_results) / len(device_results)
                    results[device] = {
                        'avg_rtf': avg_rtf,
                        'results': device_results
                    }
                    print(f"  Average RTF: {avg_rtf:.2f}x")
                
                stt.cleanup()
            else:
                print(f"  Failed to initialize STT on {device}")
                
        except Exception as e:
            print(f"  Error testing {device}: {e}")
    
    # Compare results
    if len(results) > 1:
        print(f"\nPerformance Comparison:")
        for device, data in results.items():
            print(f"  {device.upper()}: {data['avg_rtf']:.2f}x RTF")
        
        if 'cuda' in results and 'cpu' in results:
            speedup = results['cpu']['avg_rtf'] / results['cuda']['avg_rtf']
            print(f"  CUDA Speedup: {speedup:.1f}x faster than CPU")

def main():
    """Run STT GPU acceleration tests"""
    print("STT GPU Acceleration Test")
    print("="*50)
    
    # Test CUDA availability
    cuda_available = test_cuda_availability()
    
    # Test STT on CPU
    cpu_success = test_stt_device("cpu", "tiny")
    
    # Test STT on CUDA if available
    cuda_success = False
    if cuda_available:
        cuda_success = test_stt_device("cuda", "tiny")
    
    # Performance comparison
    test_performance_comparison()
    
    # Summary
    print("\n" + "="*50)
    print("STT GPU TEST SUMMARY")
    print("="*50)
    print(f"CUDA Available: {'✓' if cuda_available else '✗'}")
    print(f"CPU STT: {'✓ Working' if cpu_success else '✗ Failed'}")
    print(f"CUDA STT: {'✓ Working' if cuda_success else '✗ Failed' if cuda_available else 'N/A'}")
    
    if cuda_success:
        print("\n🚀 STT GPU acceleration is working!")
    elif cuda_available and not cuda_success:
        print("\n⚠ CUDA available but STT GPU acceleration failed")
    else:
        print("\n💡 CUDA not available - using CPU fallback")

if __name__ == "__main__":
    main()
