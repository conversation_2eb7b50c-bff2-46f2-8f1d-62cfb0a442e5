#!/usr/bin/env python3
"""
Start Voice Conversation - Ready to Talk!
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from ultra_voice_agent import UltraVoiceAgent
from core.config import VoiceAgentConfig

def main():
    print("🚀 Starting Ultra-Performance Voice Agent...")
    print("=" * 60)
    
    # Create and configure the voice agent
    config = VoiceAgentConfig()
    agent = UltraVoiceAgent(config)
    
    try:
        # Initialize the system
        print("Initializing system...")
        if not agent.initialize():
            print("❌ Failed to initialize system")
            return
        
        # Start the voice agent
        print("Starting voice agent...")
        if not agent.start():
            print("❌ Failed to start system")
            return
        
        print("✅ System ready!")
        print("\n" + "=" * 60)
        print("🎤 VOICE CONVERSATION ACTIVE")
        print("=" * 60)
        print("🗣️  Just start speaking naturally!")
        print("🤖 The AI will listen and respond with voice")
        print("⚡ GPU-accelerated for ultra-low latency")
        print("🛑 Press Ctrl+C to stop")
        print("=" * 60)
        
        # Start the conversation
        agent.start_conversation()
        
    except KeyboardInterrupt:
        print("\n\n🛑 Conversation stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        print("\n🔄 Shutting down system...")
        agent.stop()
        print("✅ System shutdown complete")

if __name__ == "__main__":
    main()
