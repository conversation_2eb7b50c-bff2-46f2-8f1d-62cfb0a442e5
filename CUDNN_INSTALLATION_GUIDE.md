# cuDNN Installation Guide for Voice Agent GPU Acceleration

## Problem
The voice agent's STT (Speech-to-Text) component requires cuDNN for GPU acceleration with Faster-Whisper. Currently getting error:
```
Could not locate cudnn_ops64_9.dll. Please make sure it is in your library path!
Invalid handle. Cannot load symbol cudnnCreateTensorDescriptor
```

## System Information
- **CUDA Version**: 12.1
- **CTranslate2 Version**: 4.6.0
- **Required cuDNN**: Version 8 for CUDA 12.x
- **Current Issue**: Missing cuDNN installation

## Solution: Install cuDNN 8 for CUDA 12.x

### Step 1: Download cuDNN
1. Go to [NVIDIA cuDNN Download Page](https://developer.nvidia.com/cudnn)
2. Create/login to NVIDIA Developer account (free)
3. Download **cuDNN v8.9.7 for CUDA 12.x** (Windows)
   - File: `cudnn-windows-x86_64-8.9.7.29_cuda12-archive.zip`

### Step 2: Install cuDNN
1. Extract the downloaded ZIP file
2. Copy files to CUDA installation directory:
   ```
   From: cudnn-windows-x86_64-8.9.7.29_cuda12-archive\
   To: C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\
   ```
   
   Specifically:
   - Copy `bin\*.dll` → `C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\bin\`
   - Copy `include\*.h` → `C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\include\`
   - Copy `lib\x64\*.lib` → `C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\lib\x64\`

### Step 3: Verify Installation
Run this command to verify cuDNN is installed:
```bash
dir "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\bin\cudnn*.dll"
```

You should see files like:
- `cudnn64_8.dll`
- `cudnn_ops_infer64_8.dll`
- `cudnn_ops_train64_8.dll`
- `cudnn_cnn_infer64_8.dll`
- etc.

### Step 4: Test GPU Acceleration
After installing cuDNN, test the STT GPU acceleration:
```bash
python test_stt_gpu.py
```

## Alternative: Use CPU-Only Mode

If you prefer not to install cuDNN, the system will automatically fall back to CPU mode, which still provides excellent performance:

### Update Configuration
Edit `core/config.py` to use CPU for STT:
```python
stt_device: str = "cpu"  # Change from "cuda" to "cpu"
```

### CPU Performance
- **CPU STT Performance**: Still very fast for real-time transcription
- **No GPU Dependencies**: Eliminates cuDNN installation requirement
- **Reliable Fallback**: System automatically falls back to CPU on GPU errors

## Performance Comparison

| Mode | Real-Time Factor | Pros | Cons |
|------|------------------|------|------|
| GPU (with cuDNN) | ~0.1-0.2x | Fastest processing | Requires cuDNN installation |
| CPU | ~0.3-0.5x | No dependencies, reliable | Slightly slower |

## Troubleshooting

### Common Issues

1. **"cudnn_ops64_9.dll not found"**
   - Install cuDNN 8 (not 9) for CUDA 12.x
   - Ensure DLLs are in CUDA bin directory

2. **"CUDA driver version insufficient"**
   - Update NVIDIA GPU drivers
   - Ensure driver supports CUDA 12.1

3. **"No CUDA devices detected"**
   - Check GPU drivers
   - Verify CUDA installation with `nvcc --version`

### Verification Commands
```bash
# Check CUDA version
nvcc --version

# Check cuDNN installation
dir "C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\bin\cudnn*.dll"

# Test CTranslate2 CUDA support
python -c "import ctranslate2; print('CUDA devices:', ctranslate2.get_cuda_device_count())"

# Test STT GPU acceleration
python test_stt_gpu.py
```

## Recommended Approach

For immediate use:
1. **Set STT to CPU mode** in configuration (reliable, fast enough)
2. **Install cuDNN later** if maximum performance is needed

For maximum performance:
1. **Install cuDNN 8** following the steps above
2. **Keep STT in CUDA mode** for fastest transcription

## Status After Fix

Once cuDNN is properly installed, you should see:
- ✅ CUDA STT initialization successful
- ✅ GPU acceleration working
- ✅ Faster transcription times
- ✅ No fallback to CPU needed
